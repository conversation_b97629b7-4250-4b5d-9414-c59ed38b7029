<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="report_vendor_bill_journal_entries">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-foreach="docs" t-as="o">
                    <div class="page" style="font-size:12px;">
                        <!-- Header Information -->
                        <div class="row">
                            <table width="100%" class="table table-bordered" style="border: 2px solid black;">
                                <tr>
                                    <td style="border: 1px solid black; padding: 5px; width: 15%;">
                                        <strong>Journal Entry</strong>
                                    </td>
                                    <td style="border: 1px solid black; padding: 5px; width: 20%;">
                                        <span t-field="o.name"/>
                                    </td>
                                    <td style="border: 1px solid black; padding: 5px; width: 15%;">
                                        <strong>Account Date</strong>
                                    </td>
                                    <td style="border: 1px solid black; padding: 5px; width: 20%;">
                                        <span t-field="o.date" t-options="{'widget': 'date', 'format': 'dd-MMM-yy'}"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid black; padding: 5px;">
                                        <strong>Currency</strong>
                                    </td>
                                    <td style="border: 1px solid black; padding: 5px;">
                                        <span t-field="o.currency_id.name"/>
                                    </td>
                                    <td style="border: 1px solid black; padding: 5px;">
                                        <strong>Subledger Document Name</strong>
                                    </td>
                                    <td style="border: 1px solid black; padding: 5px;">
                                        <span t-field="o.partner_id.name"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid black; padding: 5px;">
                                        <strong>Ledger</strong>
                                    </td>
                                    <td style="border: 1px solid black; padding: 5px;">
                                        <span t-field="o.journal_id.name"/>
                                    </td>
                                    <td style="border: 1px solid black; padding: 5px;">
                                        <strong>Subledger Document Number</strong>
                                    </td>
                                    <td style="border: 1px solid black; padding: 5px;">
                                        <span t-field="o.ref"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="border: 1px solid black; padding: 5px;">
                                        <strong>Rate Type</strong>
                                    </td>
                                    <td style="border: 1px solid black; padding: 5px;">
                                        User
                                    </td>
                                    <td style="border: 1px solid black; padding: 5px;">
                                        <strong>Exchange Rate</strong>
                                    </td>
                                    <td style="border: 1px solid black; padding: 5px;">
                                        1
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <br/>

                        <!-- Journal Items Table -->
                        <div class="row">
                            <table width="100%" class="table table-bordered" style="border: 2px solid black;">
                                <thead>
                                    <tr style="background-color: #f0f0f0;">
                                        <th style="border: 1px solid black; padding: 5px; text-align: center; width: 5%;">
                                            <strong>Line</strong>
                                        </th>
                                        <th style="border: 1px solid black; padding: 5px; text-align: center; width: 10%;">
                                            <strong>Account</strong>
                                        </th>
                                        <th style="border: 1px solid black; padding: 5px; text-align: center; width: 25%;">
                                            <strong>Account Description</strong>
                                        </th>
                                        <th style="border: 1px solid black; padding: 5px; text-align: center; width: 10%;">
                                            <strong>Analytic</strong>
                                        </th>
                                        <th style="border: 1px solid black; padding: 5px; text-align: center; width: 15%;">
                                            <strong>Entered Debits</strong>
                                        </th>
                                        <th style="border: 1px solid black; padding: 5px; text-align: center; width: 15%;">
                                            <strong>Entered Credits</strong>
                                        </th>
                                        <th style="border: 1px solid black; padding: 5px; text-align: center; width: 20%;">
                                            <strong>Description</strong>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <t t-set="total_credit" t-value="0"/>
                                    <t t-set="total_debit" t-value="0"/>
                                    <t t-set="line_number" t-value="1"/>
                                    <t t-foreach="o.line_ids" t-as="line">
                                        <tr>
                                            <td style="border: 1px solid black; padding: 5px; text-align: center;">
                                                <span t-esc="line_number"/>
                                            </td>
                                            <td style="border: 1px solid black; padding: 5px;">
                                                <span t-field="line.account_id.code"/>
                                            </td>
                                            <td style="border: 1px solid black; padding: 5px;">
                                                <span t-field="line.account_id.name"/>
                                            </td>
                                            <td style="border: 1px solid black; padding: 5px; text-align: center;">
                                                <t t-if="line.analytic_distribution">
                                                    <t t-foreach="line.analytic_distribution.keys()" t-as="analytic_id">
                                                        <t t-set="analytic_account" t-value="env['account.analytic.account'].browse(int(analytic_id))"/>
                                                        <span t-esc="analytic_account.code or '000-000'"/>
                                                    </t>
                                                </t>
                                                <t t-else="">
                                                    000-000
                                                </t>
                                            </td>
                                            <td style="border: 1px solid black; padding: 5px; text-align: right;">
                                                <t t-if="line.debit > 0">
                                                    <span t-esc="'{:,.0f}'.format(line.debit)"/>
                                                </t>
                                                <t t-else="">
                                                    0
                                                </t>
                                            </td>
                                            <td style="border: 1px solid black; padding: 5px; text-align: right;">
                                                <t t-if="line.credit > 0">
                                                    <span t-esc="'{:,.0f}'.format(line.credit)"/>
                                                </t>
                                                <t t-else="">
                                                    0
                                                </t>
                                            </td>
                                            <td style="border: 1px solid black; padding: 5px;">
                                                <span t-field="line.name"/>
                                            </td>
                                            <t t-set="total_credit" t-value="total_credit + line.credit"/>
                                            <t t-set="total_debit" t-value="total_debit + line.debit"/>
                                            <t t-set="line_number" t-value="line_number + 1"/>
                                        </tr>
                                    </t>
                                </tbody>
                                <tfoot>
                                    <tr style="background-color: #f0f0f0;">
                                        <td colspan="4" style="border: 1px solid black; padding: 5px; text-align: center;">
                                            <strong>Journal Total</strong>
                                        </td>
                                        <td style="border: 1px solid black; padding: 5px; text-align: right;">
                                            <strong t-esc="'{:,.0f}'.format(total_debit)"/>
                                        </td>
                                        <td style="border: 1px solid black; padding: 5px; text-align: right;">
                                            <strong t-esc="'{:,.0f}'.format(total_credit)"/>
                                        </td>
                                        <td style="border: 1px solid black; padding: 5px;">
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </t>
            </t>
        </t>
    </template>

    <!-- Report Action -->
    <record id="action_report_vendor_bill_journal_entries" model="ir.actions.report">
        <field name="name">Vendor Bill</field>
        <field name="model">account.move</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">gleor_vendor_bills.report_vendor_bill_journal_entries</field>
        <field name="report_file">gleor_vendor_bills.report_vendor_bill_journal_entries</field>
        <field name="binding_model_id" ref="account.model_account_move"/>
        <field name="binding_type">report</field>
        <field name="paperformat_id" ref="base.paperformat_a4"/>
    </record>

</odoo>
