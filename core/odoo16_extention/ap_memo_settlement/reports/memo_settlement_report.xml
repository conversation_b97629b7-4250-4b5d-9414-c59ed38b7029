<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Simple Memo Settlement Report -->
    <template id="memo_settlement_report_template">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-foreach="docs" t-as="doc">
                    <div class="page">

                        <!-- Simple Header -->
                        <div class="row">
                            <div class="col-12 text-center">
                                <h2><strong>MEMO SETTLEMENT REPORT</strong></h2>
                                <hr/>
                            </div>
                        </div>

                        <!-- Settlement Information -->
                        <div class="row mt-3">
                            <div class="col-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td style="width: 40%;"><strong>Settlement Number:</strong></td>
                                        <td><span t-field="doc.name"/></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Date:</strong></td>
                                        <td><span t-field="doc.date"/></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Memo:</strong></td>
                                        <td><span t-field="doc.memo_id.name"/></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Journal:</strong></td>
                                        <td><span t-field="doc.journal_id.name"/></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td style="width: 30%;"><strong>Status:</strong></td>
                                        <td>
                                            <span t-if="doc.state == 'draft'" class="badge badge-warning">Draft</span>
                                            <span t-if="doc.state == 'posted'" class="badge badge-success">Posted</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Company:</strong></td>
                                        <td><span t-field="doc.company_id.name"/></td>
                                    </tr>
                                    <tr t-if="doc.analytic_distribution">
                                        <td><strong>Contract:</strong></td>
                                        <td>
                                            <t t-foreach="doc.analytic_distribution.items()" t-as="analytic">
                                                <t t-set="analytic_name" t-value="env['account.analytic.account'].browse(int(analytic[0])).name"/>
                                                <span t-esc="analytic_name"/>
                                            </t>
                                        </td>
                                    </tr>
                                    <tr t-if="doc.memo_move_id">
                                        <td><strong>Journal Entry:</strong></td>
                                        <td><span t-field="doc.memo_move_id.name"/></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Settlement Details -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h4><strong>Settlement Details</strong></h4>
                                <table class="table table-bordered">
                                    <thead class="thead-light">
                                        <tr>
                                            <th style="width: 5%;">No.</th>
                                            <th style="width: 12%;">Bill Number</th>
                                            <th style="width: 8%;">Date</th>
                                            <th style="width: 15%;">Vendor</th>
                                            <th style="width: 10%;">Reference</th>
                                            <th style="width: 18%;">Description</th>
                                            <th style="width: 12%;">Account</th>
                                            <th style="width: 10%;">No Faktur</th>
                                            <th style="width: 10%;">Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <t t-set="line_number" t-value="1"/>
                                        <t t-set="total_amount" t-value="0"/>
                                        <t t-foreach="doc.bill_ids" t-as="line">
                                            <tr>
                                                <td class="text-center"><t t-esc="line_number"/></td>
                                                <td><span t-field="line.move_id.name"/></td>
                                                <td><span t-field="line.date"/></td>
                                                <td><span t-field="line.partner_id.name"/></td>
                                                <td><span t-field="line.ref"/></td>
                                                <td><span t-field="line.label"/></td>
                                                <td>
                                                    <span t-field="line.account_expense_id.code"/> -
                                                    <span t-field="line.account_expense_id.name"/>
                                                </td>
                                                <td><span t-field="line.move_id.no_faktur"/></td>
                                                <td class="text-right">
                                                    <span t-field="line.amount_total_signed" t-options="{'widget': 'monetary', 'display_currency': line.currency_id}"/>
                                                </td>
                                            </tr>
                                            <t t-set="line_number" t-value="line_number + 1"/>
                                            <t t-set="total_amount" t-value="total_amount + line.amount_total_signed"/>
                                        </t>
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-active">
                                            <td colspan="8" class="text-right"><strong>Total Amount:</strong></td>
                                            <td class="text-right">
                                                <strong t-esc="'%.2f' % total_amount"/>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>

                        <!-- Journal Entries (if posted) -->
                        <div class="row mt-4" t-if="doc.state == 'posted' and doc.journal_ids">
                            <div class="col-12">
                                <h4><strong>Journal Entries</strong></h4>
                                <p><strong>Journal Entry:</strong> <span t-field="doc.memo_move_id.name"/></p>
                                <table class="table table-bordered">
                                    <thead class="thead-light">
                                        <tr>
                                            <th style="width: 8%;">No.</th>
                                            <th style="width: 25%;">Account</th>
                                            <th style="width: 30%;">Description</th>
                                            <th style="width: 12%;">Debit</th>
                                            <th style="width: 12%;">Credit</th>
                                            <th style="width: 13%;">Balance</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <t t-set="journal_line_number" t-value="1"/>
                                        <t t-set="total_debit" t-value="0"/>
                                        <t t-set="total_credit" t-value="0"/>
                                        <t t-foreach="doc.journal_ids" t-as="journal_line">
                                            <tr>
                                                <td class="text-center"><t t-esc="journal_line_number"/></td>
                                                <td>
                                                    <span t-field="journal_line.account_id.code"/> -
                                                    <span t-field="journal_line.account_id.name"/>
                                                </td>
                                                <td><span t-field="journal_line.name"/></td>
                                                <td class="text-right">
                                                    <span t-field="journal_line.debit" t-options="{'widget': 'monetary', 'display_currency': journal_line.currency_id}"/>
                                                </td>
                                                <td class="text-right">
                                                    <span t-field="journal_line.credit" t-options="{'widget': 'monetary', 'display_currency': journal_line.currency_id}"/>
                                                </td>
                                                <td class="text-right">
                                                    <span t-field="journal_line.balance" t-options="{'widget': 'monetary', 'display_currency': journal_line.currency_id}"/>
                                                </td>
                                            </tr>
                                            <t t-set="journal_line_number" t-value="journal_line_number + 1"/>
                                            <t t-set="total_debit" t-value="total_debit + journal_line.debit"/>
                                            <t t-set="total_credit" t-value="total_credit + journal_line.credit"/>
                                        </t>
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-active">
                                            <td colspan="3" class="text-right"><strong>Total:</strong></td>
                                            <td class="text-right"><strong t-esc="'%.2f' % total_debit"/></td>
                                            <td class="text-right"><strong t-esc="'%.2f' % total_credit"/></td>
                                            <td class="text-right"><strong t-esc="'%.2f' % (total_debit - total_credit)"/></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>

                        <!-- Simple Footer -->
                        <div class="row mt-5">
                            <div class="col-12">
                                <hr/>
                                <p class="text-muted text-center">
                                    Generated on <span t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d %H:%M:%S')"/>
                                </p>
                            </div>
                        </div>
                    </div>
                </t>
            </t>
        </t>
    </template>

    <!-- Report Action -->
    <record id="action_memo_settlement_report" model="ir.actions.report">
        <field name="name">Memo Settlement Report</field>
        <field name="model">memo.settlement</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">ap_memo_settlement.memo_settlement_report_template</field>
        <field name="report_file">ap_memo_settlement.memo_settlement_report_template</field>
        <field name="binding_model_id" ref="ap_memo_settlement.model_memo_settlement"/>
        <field name="binding_type">report</field>
    </record>

</odoo>
